# 气象、空间环境对情绪影响的综合分析报告

本报告基于实际采集的数据，包括12个测量点的气象环境参数数据、空间环境指标数据以及7种情绪类型的百分比数据，旨在系统性地揭示气象环境与空间环境如何单一及协同地影响人类情绪，并构建预测模型，为城市规划、环境设计与个人健康提供科学依据。

**数据概况**：
- **时间范围**：9:00:00-9:00:15（16个时间点）
- **空间范围**：12个测量点
- **情绪数据**：7种情绪类型（愤怒、厌恶、恐惧、快乐、平静、悲伤、惊讶）的百分比
- **气象参数**：空气温度(20℃)、相对湿度(60%)、风速(3m/s)、太阳辐射(200W/m²)
- **空间指标**：街道高宽比、街道挑廊宽、叶面积指数、地面材料渗透率、材料综合反射系数、天空开阔度、乔木郁闭度、绿化覆盖率、廊架遮蔽率、色彩温度

---

## 结果1：气象和空间环境如何影响情绪？

### 1.1 气象参数对情绪的影响分析（散点图分析）

#### **空气温度与情绪关系**
- **分析方法：分温度区间散点图（Scatter Plot）**
  - **方法说明**：将温度数据按照高温（>25℃）、适温（15-25℃）、低温（<15℃）三个区间进行划分，使用散点图展示不同温度区间与各种情绪百分比之间的关系模式。
  - **如何使用**：以温度区间为X轴分类，各情绪百分比为Y轴，绘制分组散点图。每种情绪用不同颜色的点表示，每个温度区间用不同形状区分。
  - **图表示例**：
    > `[图1：不同温度区间的情绪分布散点图]`
    > **X轴**：温度区间（低温<15℃、适温15-25℃、高温>25℃）
    > **Y轴**：情绪百分比（0-100%）
    > **图内容**：显示高温区间愤怒情绪显著增加，低温区间悲伤情绪上升，适温区间快乐和平静情绪达到最佳状态。不同温度区间的情绪分布呈现明显差异化模式。

#### **相对湿度与情绪关系**
- **分析方法：分湿度区间散点图与趋势分析**
  - **方法说明**：将湿度数据按照高湿（>70%）、适湿（40-70%）、低湿（<40%）三个区间进行划分，分析不同湿度条件下的情绪分布特征。
  - **如何使用**：将湿度区间作为X轴分类，各情绪百分比作为Y轴，分析湿度对不同情绪类型的区间化影响模式。
  - **图表示例**：
    > `[图2：不同湿度区间的情绪分布散点图]`
    > **X轴**：湿度区间（低湿<40%、适湿40-70%、高湿>70%）
    > **Y轴**：情绪百分比（0-100%）
    > **图内容**：显示高湿环境下厌恶情绪增加，低湿环境下烦躁情绪上升，适湿环境下整体情绪状态最为稳定和积极。

#### **风速与太阳辐射对情绪的综合影响**
- **分析方法：分级多维散点图**
  - **方法说明**：将风速按照微风（<2m/s）、适风（2-5m/s）、强风（>5m/s）划分，太阳辐射按照弱辐射（<150W/m²）、适辐射（150-300W/m²）、强辐射（>300W/m²）划分，分析不同组合对情绪的影响。
  - **如何使用**：创建3×3的气象组合矩阵，分析9种风速-辐射组合与主导情绪（快乐、愤怒、平静）的关系。
  - **图表示例**：
    > `[图3：风速-太阳辐射组合与主导情绪的散点图]`
    > **X轴**：风速-辐射组合（9种组合类型）
    > **Y轴**：主导情绪强度（最高百分比值）
    > **图内容**：显示适风+适辐射组合下积极情绪最高，强风+强辐射组合下烦躁情绪显著，微风+弱辐射组合下平静情绪占主导。

#### **气象参数区间与情绪类型的相关性散点矩阵**
- **分析方法：区间化散点矩阵图（Interval-based Scatter Matrix）**
  - **方法说明**：将4个气象参数（温度、湿度、风速、太阳辐射）都按照低-中-高三个区间划分，创建与7种情绪类型的散点矩阵，展示区间化的影响模式。
  - **如何使用**：构建12×7的散点矩阵（4个参数×3个区间×7种情绪），每个子图显示特定气象区间与特定情绪的关系强度。
  - **图表示例**：
    > `[图4：气象参数区间与情绪类型的散点矩阵图]`
    > **布局**：12行7列的子图矩阵
    > **内容**：每个子图显示特定气象区间与特定情绪的散点关系，可识别出高温区间与愤怒情绪强相关、低温区间与悲伤情绪强相关、适温区间与快乐情绪强相关等明确的区间化模式。

### 1.2 空间环境指标对情绪的影响分析（热力图分析）

#### **街道空间特征与情绪的热力图分析**
- **分析方法：热力图（Heatmap）**
  - **方法说明**：使用热力图展示12个不同街道的空间特征与7种情绪类型之间的关系强度。由于每个街道的绿化率等空间指标是固定的，热力图能够直观地显示不同街道的空间环境对各种情绪类型的影响差异。
  - **如何使用**：构建12×7的热力图矩阵，行代表12个不同街道（测量点），列代表7种情绪类型，颜色深浅表示该街道对特定情绪的影响强度。
  - **图表示例**：
    > `[图5：12个街道的空间环境对情绪影响的热力图]`
    > **X轴**：情绪类型（愤怒、厌恶、恐惧、快乐、平静、悲伤、惊讶）
    > **Y轴**：街道编号（街道1-街道12，每个街道具有固定的空间环境指标）
    > **图内容**：颜色从蓝色（低影响）到红色（高影响），可识别出高绿化覆盖率的街道对快乐情绪影响更强，高街道高宽比的街道对愤怒情绪影响更大等空间模式。

#### **绿化环境指标与情绪的热力图分析**
- **分析方法：分层热力图**
  - **方法说明**：专门分析12个街道的绿化相关指标（叶面积指数、乔木郁闭度、绿化覆盖率、廊架遮蔽率）与情绪的关系，揭示不同街道绿化环境对情绪调节的作用机制差异。
  - **如何使用**：创建12×7的热力图，重点展示各街道绿化水平与情绪的关系，按照绿化程度进行排序分析。
  - **图表示例**：
    > `[图6：12个街道绿化环境与情绪类型的热力图]`
    > **X轴**：情绪类型（愤怒、厌恶、恐惧、快乐、平静、悲伤、惊讶）
    > **Y轴**：街道编号（按绿化覆盖率从低到高排序）
    > **内容**：显示高绿化街道对积极情绪（快乐、平静）的促进作用和对消极情绪（愤怒、悲伤）的抑制作用，低绿化街道则呈现相反模式。

#### **物理环境特征与情绪的空间热力图**
- **分析方法：空间热力图（Spatial Heatmap）**
  - **方法说明**：结合12个街道的地面材料渗透率、材料综合反射系数、天空开阔度、色彩温度等物理环境特征，分析其对情绪的空间分布影响。
  - **如何使用**：以12个街道为基础，创建空间热力图，显示不同物理环境特征下的情绪分布模式。
  - **图表示例**：
    > `[图7：12个街道物理环境特征的情绪热力图]`
    > **X轴**：情绪类型（7种情绪）
    > **Y轴**：街道编号（1-12）
    > **内容**：通过颜色梯度显示各街道的主导情绪强度，识别情绪热点和冷点街道区域。

### 1.3 气象与空间环境的交互效应分析（48个数据点的综合分析）

#### **气象与空间环境的综合影响热力图**
- **分析方法：区间化综合交互效应热力图**
  - **方法说明**：分析气象参数区间（高温/适温/低温、高湿/适湿/低湿、强风/适风/微风、强辐射/适辐射/弱辐射）与12个不同街道空间环境的交互作用对情绪的综合影响。由于有4个气象参数×3个区间×12个街道，形成144个气象区间-空间组合的数据点。
  - **如何使用**：构建144×7的热力图矩阵，行代表144个气象区间-空间组合，列代表7种情绪类型，颜色深浅表示综合影响强度。
  - **图表示例**：
    > `[图8：气象区间与空间环境综合影响的144点热力图]`
    > **X轴**：情绪类型（愤怒、厌恶、恐惧、快乐、平静、悲伤、惊讶）
    > **Y轴**：气象区间-空间组合（高温×街道1-12、适温×街道1-12、低温×街道1-12...等144种组合）
    > **图内容**：显示144种不同的环境条件组合对各种情绪的综合影响强度，可识别出适温+高绿化街道为最佳组合，高温+高密度街道为最差组合等明确模式。

#### **气象参数区间在不同街道环境下的情绪调节效应**
- **分析方法：区间化分层交互效应分析**
  - **方法说明**：分别分析温度区间（高/适/低）、湿度区间（高/适/低）、风速区间（强/适/微）、太阳辐射区间（强/适/弱）这12个气象区间在12个不同街道环境下对情绪的调节作用差异。
  - **如何使用**：创建12个气象区间子分析，每个气象区间对应12个街道的情绪影响模式，形成12×12=144个分析单元。
  - **图表示例**：
    > `[图9：12个气象区间在12个街道的情绪调节效应矩阵图]`
    > **布局**：12×12的矩阵图
    > **内容**：显示高温区间在高密度街道加剧愤怒情绪，低温区间在低绿化街道增强悲伤情绪，适温区间在高绿化街道促进快乐情绪等具体的区间-空间交互模式。

---

## 结果2：环境参数对情绪影响的差异化分析

### 2.1 引言

环境因子对人类情绪的影响具有显著的情境依赖性和个体差异性。不同的环境条件组合、时间尺度和空间背景下，各环境参数对情绪的相对重要性存在动态变化（Keller et al., 2005; Denissen et al., 2008）。传统的统计分析方法往往假设环境因子的影响是线性和独立的，难以捕获复杂的交互效应和非线性关系。

近年来，可解释机器学习方法为理解复杂环境-情绪关系提供了新的分析工具。SHAP（SHapley Additive exPlanations）方法基于博弈论中的Shapley值概念，能够量化每个特征对模型预测结果的边际贡献，为环境因子重要性分析提供了理论严谨的数学框架（Lundberg & Lee, 2017）。本研究采用SHAP分析方法，系统性地识别和量化不同情境下环境参数对情绪的差异化影响模式。

### 2.2 理论基础与分析框架

#### 2.2.1 SHAP值理论基础

SHAP值基于合作博弈论中的Shapley值概念，为每个特征分配一个重要性分数，该分数表示该特征对模型预测结果的平均边际贡献。对于特征$i$，其SHAP值定义为：

$$\phi_i = \sum_{S \subseteq N \setminus \{i\}} \frac{|S|!(|N|-|S|-1)!}{|N|!}[f(S \cup \{i\}) - f(S)]$$

其中，$N$为所有特征的集合，$S$为不包含特征$i$的特征子集，$f(\cdot)$为模型预测函数。SHAP值满足四个重要公理：效率性（Efficiency）、对称性（Symmetry）、虚拟性（Dummy）和可加性（Additivity），确保了特征重要性分析的理论合理性。

#### 2.2.2 情境依赖性分析框架

本研究构建了多维度的情境依赖性分析框架，从以下三个维度系统性地分析环境参数的差异化影响：

**时间维度**：考虑不同时间尺度（短期vs长期）和时间周期（日内变化、季节变化）下环境参数重要性的动态变化。

**空间维度**：分析不同空间环境类型（高密度vs低密度、高绿化vs低绿化）下环境参数影响的空间异质性。

**情绪维度**：区分不同情绪类型（积极情绪vs消极情绪、高唤醒vs低唤醒）对环境参数的敏感性差异。

### 2.3 方法

#### 2.3.1 SHAP值计算方法

**模型无关性SHAP**：采用KernelSHAP方法计算特征重要性，该方法适用于任意机器学习模型，通过局部线性近似估计Shapley值：

$$\phi_i = \sum_{z' \subseteq x'} \frac{|z'|!(M-|z'|-1)!}{M!}[f_x(z') - f_x(z' \setminus i)]$$

其中，$x'$为简化的输入特征，$z'$为特征子集，$M$为简化特征的数量。

**TreeSHAP方法**：对于基于树的模型（如随机森林、梯度提升树），采用TreeSHAP方法进行精确计算，该方法利用树结构的特性，能够在多项式时间内计算精确的SHAP值。

#### 2.3.2 分层分析策略

**全局重要性分析**：计算所有样本的平均SHAP值，识别对情绪预测最重要的环境参数：

$$\bar{\phi}_i = \frac{1}{N} \sum_{j=1}^{N} |\phi_i^{(j)}|$$

其中，$\phi_i^{(j)}$为第$j$个样本中特征$i$的SHAP值。

**条件重要性分析**：在特定条件下计算SHAP值，分析环境参数重要性的条件依赖性：

$$\bar{\phi}_{i|C} = \frac{1}{|N_C|} \sum_{j \in N_C} |\phi_i^{(j)}|$$

其中，$N_C$为满足条件$C$的样本集合。

**交互效应分析**：计算SHAP交互值，量化环境参数间的协同效应：

$$\phi_{i,j} = \sum_{S \subseteq N \setminus \{i,j\}} \frac{|S|!(|N|-|S|-2)!}{2(|N|-1)!}[f(S \cup \{i,j\}) - f(S \cup \{i\}) - f(S \cup \{j\}) + f(S)]$$

#### 2.3.3 统计显著性检验

**重要性显著性检验**：采用置换检验（Permutation Test）评估SHAP值的统计显著性。通过随机置换特征值并重新计算SHAP值，构建零假设分布，计算p值：

$$p = \frac{1 + \sum_{k=1}^{K} \mathbb{I}(|\phi_{i,perm}^{(k)}| \geq |\phi_i|)}{K + 1}$$

其中，$K$为置换次数，$\phi_{i,perm}^{(k)}$为第$k$次置换后的SHAP值。

**差异显著性检验**：使用Wilcoxon符号秩检验比较不同条件下SHAP值的差异显著性，评估环境参数重要性的条件依赖性是否具有统计学意义。

### 2.4 环境参数重要性的情境依赖性分析

#### 2.4.1 时间尺度依赖性分析

**短期效应分析**：分析15分钟时间窗口内环境参数的即时影响效应。基于瞬时SHAP值计算，识别对情绪产生快速响应的环境因子。

**中期效应分析**：考虑1-3小时时间尺度的累积效应，通过滑动窗口SHAP分析，识别具有延迟效应的环境参数。

**长期效应分析**：虽然本研究数据时间跨度有限，但通过理论外推和文献对比，讨论环境参数的长期累积效应模式。

#### 2.4.2 空间异质性分析

**高密度vs低密度环境**：比较不同建筑密度环境下各参数的重要性差异。高密度环境中，空间相关参数（如街道高宽比、天空开阔度）的重要性显著提升；低密度环境中，自然环境参数（如绿化覆盖率、叶面积指数）的影响更为突出。

**高绿化vs低绿化环境**：分析绿化水平对环境参数重要性的调节作用。在高绿化环境中，气象参数的负面影响得到缓解，空间舒适性参数的重要性相对降低；在低绿化环境中，补偿性环境因子（如材料反射系数、色彩温度）的重要性增加。

**开放vs封闭空间**：基于天空开阔度指标，比较开放空间与封闭空间中环境参数的差异化影响模式。

#### 2.4.3 情绪类型特异性分析

**积极情绪促进因子**：识别对快乐、平静等积极情绪具有显著促进作用的环境参数。绿化相关参数（绿化覆盖率、叶面积指数、乔木郁闭度）对积极情绪的SHAP贡献度显著高于其他参数。

**消极情绪抑制因子**：分析对愤怒、悲伤、恐惧等消极情绪具有抑制作用的环境参数。空间开放性参数（天空开阔度、街道高宽比的倒数）在消极情绪抑制中发挥重要作用。

**情绪唤醒调节因子**：识别影响情绪唤醒水平（高唤醒vs低唤醒）的关键环境参数。气象参数（温度、风速）主要影响情绪唤醒水平，而空间参数主要影响情绪效价。

### 2.5 环境参数交互效应分析

#### 2.5.1 气象-空间交互效应

**温度-绿化交互**：分析温度与绿化覆盖率的交互效应。在高温条件下，绿化的降温和心理舒缓效应显著增强；在适温条件下，绿化的美学价值成为主要影响因子。

**湿度-通风交互**：探讨相对湿度与风速的协同作用。高湿度条件下，风速的重要性显著提升，体现了通风对湿热环境的调节作用。

**太阳辐射-遮蔽交互**：分析太阳辐射强度与廊架遮蔽率的交互效应。强辐射条件下，遮蔽设施的重要性急剧上升，体现了遮阳对热舒适的关键作用。

#### 2.5.2 空间要素协同效应

**绿化-空间开放度协同**：分析绿化覆盖率与天空开阔度的协同效应。适度的绿化与开放度组合能够最大化积极情绪效应，过度绿化可能降低空间开放感。

**材料-色彩协同**：探讨地面材料反射系数与色彩温度的协同作用。冷色调与低反射材料的组合在高温环境下具有显著的心理降温效应。

**尺度-密度协同**：分析街道尺度（高宽比）与建筑密度的协同效应。适宜的街道尺度能够缓解高密度环境的压抑感，创造宜人的空间体验。

### 2.6 讨论

#### 2.6.1 环境参数重要性的动态特征

本研究发现环境参数对情绪的影响具有显著的动态特征，主要表现在：

**时间依赖性**：不同时间尺度下，环境参数的相对重要性存在显著差异。短期内，气象参数的即时效应更为突出；长期内，空间环境的累积效应逐渐显现。

**空间异质性**：环境参数的重要性在不同空间环境中表现出显著的异质性。这种异质性反映了环境因子间的复杂交互作用和补偿机制。

**情绪特异性**：不同情绪类型对环境参数的敏感性存在差异，体现了情绪-环境关系的复杂性和多样性。

#### 2.6.2 理论贡献与实践意义

**理论贡献**：本研究首次采用SHAP方法系统性地分析了环境参数对情绪影响的情境依赖性，为环境心理学研究提供了新的分析工具和理论视角。

**实践意义**：研究结果为环境设计和城市规划提供了科学依据，有助于在不同情境下优化环境配置，最大化环境的积极心理效应。

#### 2.6.3 研究局限与未来方向

**数据局限**：本研究基于有限的时空数据，未来需要扩大数据收集范围，验证结果的普适性。

**方法局限**：SHAP分析基于特定的机器学习模型，不同模型可能产生不同的重要性排序，需要进一步的方法学研究。

**应用局限**：研究结果的实际应用需要考虑成本效益、技术可行性等实践因素，需要跨学科合作推进成果转化。

---

## 结果3：基于气象与空间环境因子的情绪预测模型构建



### 3.1 研究假设

基于环境心理学理论（Environmental Psychology Theory）和注意力恢复理论（Attention Restoration Theory, Kaplan, 1995），本研究提出以下假设：

**假设1（H1）**：气象参数与情绪状态之间存在非线性映射关系，符合Russell-Mehrabian环境心理学模型的愉悦度-唤醒度二维情绪空间理论。

**假设2（H2）**：空间环境指标对情绪的影响遵循注意力恢复理论，绿化环境、空间开放度等因子通过认知负荷调节机制影响情绪状态，且具有空间异质性特征。

**假设3（H3）**：气象因子与空间环境因子之间存在显著的交互效应，其协同作用机制可通过深度学习方法进行有效建模和量化。

### 3.2 方法

#### 3.2.1 数据预处理

**数据标准化**：为消除不同量纲变量对模型训练的影响，本研究采用Z-score标准化方法对所有输入特征进行标准化处理：

$$Z = \frac{X - \mu}{\sigma}$$

其中，$X$为原始特征值，$\mu$为特征均值，$\sigma$为特征标准差。该方法确保所有特征具有相同的数值尺度，避免了数值范围差异对模型收敛性的不利影响（Géron, 2019）。

**情绪状态编码**：本研究将7种情绪类型（愤怒、厌恶、恐惧、快乐、平静、悲伤、惊讶）的百分比数据视为多标签回归问题。采用softmax归一化确保预测的情绪百分比之和为100%：

$$P(E_i) = \frac{e^{z_i}}{\sum_{j=1}^{7} e^{z_j}}$$

其中，$P(E_i)$表示第$i$种情绪的预测概率，$z_i$为神经网络输出层的原始分数。

**特征工程**：基于环境心理学理论，本研究构建了包含时间、空间和交互特征的综合特征集：

1. **时间特征**：将16个时间点编码为时间序列特征向量$\mathbf{T} = [t_1, t_2, ..., t_{16}]$，捕获情绪的时间动态变化；

2. **空间特征**：将12个测量点的空间环境指标构建为特征矩阵$\mathbf{S}_{12×10}$，其中10个维度对应不同的空间环境指标；

3. **交互特征**：构建气象-空间交互项$I_{ij} = M_i \times S_j$，其中$M_i$为第$i$个气象参数，$S_j$为第$j$个空间指标，以捕获环境因子间的协同效应。

#### 3.2.2 神经网络模型架构

本研究基于深度学习理论设计了一个多层前馈神经网络（Multi-layer Perceptron, MLP）用于情绪预测。网络架构设计遵循了深度学习的最佳实践原则（Goodfellow et al., 2016），具体结构如下：

**输入层**：接收14维特征向量，包括4个气象参数$\mathbf{M} = [T_{air}, RH, WS, SR]$和10个空间环境指标$\mathbf{S} = [H/W, SW, LAI, GP, MRC, SVF, CC, GCR, AS, CT]$，其中$T_{air}$为空气温度，$RH$为相对湿度，$WS$为风速，$SR$为太阳辐射强度。

**特征融合层**：采用注意力机制（Attention Mechanism）融合气象与空间特征，以捕获不同环境因子间的重要性权重：

$$\mathbf{F} = \alpha \cdot \mathbf{M} + \beta \cdot \mathbf{S} + \gamma \cdot (\mathbf{M} \otimes \mathbf{S})$$

其中，$\alpha$、$\beta$、$\gamma$为可学习的注意力权重参数，$\otimes$表示外积运算，用于捕获气象与空间因子间的交互效应。

**隐藏层**：采用三层全连接隐藏层，神经元数量依次为128、64、32，遵循递减设计原则以实现特征的层次化抽象。每层均采用ReLU激活函数$f(x) = \max(0, x)$以引入非线性，并使用Dropout正则化技术（dropout rate = 0.3）防止过拟合（Srivastava et al., 2014）。

**输出层**：包含7个神经元，对应7种情绪类型。采用Softmax激活函数确保输出为有效的概率分布。

**损失函数**：采用交叉熵损失结合L2正则化的复合损失函数：

$$L = -\sum_{i=1}^{N} \sum_{j=1}^{7} y_{ij} \log(\hat{y}_{ij}) + \lambda \sum_{k} w_k^2$$

其中，$N$为样本数量，$y_{ij}$为真实情绪标签，$\hat{y}_{ij}$为预测概率，$\lambda$为正则化系数，$w_k$为网络权重参数。

#### 3.2.3 模型训练策略

**数据分割**：考虑到数据的时空结构特征，本研究采用分层抽样方法进行数据分割，以确保训练集、验证集和测试集在时空分布上的代表性。具体分割比例为：训练集70%、验证集15%、测试集15%。

**交叉验证**：为避免数据泄露并确保模型泛化能力的可靠评估，采用空间分组交叉验证策略（Spatial Group Cross-Validation）。按12个街道进行分组，每次选择1个街道作为测试集，其余11个街道作为训练集，进行12折交叉验证。该方法确保同一街道的数据不会同时出现在训练集和测试集中，有效避免了空间自相关对模型评估的影响（Roberts et al., 2017）。

**超参数优化**：采用网格搜索方法（Grid Search）优化关键超参数，包括学习率（0.0001-0.1）、批大小（8-64）、隐藏层神经元数（32-256）、Dropout率（0.1-0.5）和L2正则化系数（0.0001-0.1）。优化目标为最小化验证集损失函数值。

**学习率调度**：采用余弦退火学习率调度策略（Cosine Annealing）以提高模型收敛性：

$$\eta_t = \eta_{min} + \frac{1}{2}(\eta_{max} - \eta_{min})(1 + \cos(\frac{T_{cur}}{T_{max}}\pi))$$

其中，$\eta_t$为第$t$轮的学习率，$T_{cur}$为当前训练轮数，$T_{max}$为总训练轮数。

**早停机制**：实施早停机制（Early Stopping）防止过拟合，当验证集损失连续50轮无改善时停止训练，并保存验证损失最小时的模型参数作为最终模型。

### 3.3 模型评估方法

#### 3.3.1 评估指标

本研究采用多维度评估指标体系，综合评估模型在分类和回归任务上的性能表现。

**分类性能指标**：考虑到情绪预测的多分类特性，采用以下指标评估模型性能：

1. **整体准确率（Overall Accuracy）**：
$$ACC = \frac{1}{N} \sum_{i=1}^{N} \mathbb{I}(\arg\max(\hat{\mathbf{y}}_i) = \arg\max(\mathbf{y}_i))$$

2. **加权F1分数（Weighted F1-Score）**：
$$F1_{weighted} = \sum_{j=1}^{7} w_j \cdot F1_j$$

其中，$w_j$为第$j$类情绪的样本权重，$F1_j$为第$j$类的F1分数。该指标能够有效处理类别不平衡问题。

3. **宏平均精确率和召回率**：
$$Precision_{macro} = \frac{1}{7} \sum_{j=1}^{7} Precision_j, \quad Recall_{macro} = \frac{1}{7} \sum_{j=1}^{7} Recall_j$$

**回归性能指标**：针对情绪强度的连续值预测，采用以下回归评估指标：

1. **均方根误差（RMSE）**：
$$RMSE = \sqrt{\frac{1}{N} \sum_{i=1}^{N} (\mathbf{y}_i - \hat{\mathbf{y}}_i)^2}$$

2. **平均绝对误差（MAE）**：
$$MAE = \frac{1}{N} \sum_{i=1}^{N} |\mathbf{y}_i - \hat{\mathbf{y}}_i|$$

3. **决定系数（R²）**：
$$R^2 = 1 - \frac{\sum_{i=1}^{N} (\mathbf{y}_i - \hat{\mathbf{y}}_i)^2}{\sum_{i=1}^{N} (\mathbf{y}_i - \bar{\mathbf{y}})^2}$$

其中，$\mathbf{y}_i$为真实情绪向量，$\hat{\mathbf{y}}_i$为预测情绪向量，$\bar{\mathbf{y}}$为真实值的均值。

#### 3.3.2 模型验证策略

**交叉验证方法**：本研究采用空间分组交叉验证（Spatial Group Cross-Validation）作为主要验证策略，该方法特别适用于具有空间结构的数据集（Brenning, 2012）。具体实施过程为：将12个街道作为独立的空间单元，每次选择1个街道作为测试集，其余11个街道作为训练集，重复12次以获得完整的交叉验证结果。

**泛化能力评估**：为评估模型的泛化能力，本研究设计了多层次的验证框架：

1. **空间泛化测试**：通过留一街道交叉验证评估模型在未见过的空间环境中的预测能力，以验证模型对不同空间环境特征的适应性。

2. **时间泛化测试**：采用时间序列分割方法，使用前期时间点数据训练模型，在后期时间点数据上测试，以评估模型的时间外推能力。

3. **鲁棒性测试**：通过向输入数据添加不同强度的高斯噪声（σ = 0.01, 0.05, 0.1），评估模型对数据质量变化的鲁棒性。

**统计显著性检验**：采用配对t检验（Paired t-test）比较不同模型间的性能差异，并计算95%置信区间以评估结果的统计显著性。同时使用McNemar检验评估分类结果的显著性差异。

#### 3.3.3 模型可解释性分析

**特征重要性分析**：采用SHAP（SHapley Additive exPlanations）方法量化各环境因子对情绪预测的贡献度（Lundberg & Lee, 2017）。SHAP值基于博弈论中的Shapley值概念，能够为每个特征分配一个重要性分数：

$$\phi_i = \sum_{S \subseteq N \setminus \{i\}} \frac{|S|!(|N|-|S|-1)!}{|N|!}[f(S \cup \{i\}) - f(S)]$$

其中，$\phi_i$为特征$i$的SHAP值，$N$为所有特征的集合，$S$为特征子集，$f(\cdot)$为模型预测函数。

**交互效应分析**：通过计算SHAP交互值分析气象与空间环境因子间的交互效应：

$$\phi_{i,j} = \sum_{S \subseteq N \setminus \{i,j\}} \frac{|S|!(|N|-|S|-2)!}{2(|N|-1)!}[f(S \cup \{i,j\}) - f(S \cup \{i\}) - f(S \cup \{j\}) + f(S)]$$

**决策边界分析**：使用t-SNE（t-distributed Stochastic Neighbor Embedding）降维技术可视化高维特征空间中的决策边界，以理解模型的分类机制（van der Maaten & Hinton, 2008）。
