# 气象、空间环境对情绪影响的综合分析报告



**数据概况**：
- **时间范围**：9:00:00-9:00:15（16个时间点）
- **空间范围**：12个测量点
- **情绪数据**：7种情绪类型（愤怒、厌恶、恐惧、快乐、平静、悲伤、惊讶）的百分比
- **气象参数**：空气温度(20℃)、相对湿度(60%)、风速(3m/s)、太阳辐射(200W/m²)
- **空间指标**：街道高宽比、街道挑廊宽、叶面积指数、地面材料渗透率、材料综合反射系数、天空开阔度、乔木郁闭度、绿化覆盖率、廊架遮蔽率、色彩温度

---

## 结果1：环境因子对情绪影响的描述性统计分析

### 1.1 引言

#### 1.1.1 研究背景

环境心理学研究表明，物理环境对人类情绪状态具有显著影响（Mehta, 2013）。气象条件和空间环境作为两类重要的环境因子，通过不同的生理和心理机制影响个体的情绪体验。描述性统计分析作为数据分析的基础步骤，能够揭示环境因子与情绪变量间的基本关联模式，为后续的深入分析提供重要依据。

#### 1.1.2 分析目标

本部分旨在通过系统性的描述性统计分析，全面描述气象参数和空间环境指标对7种基本情绪类型的影响特征。具体目标包括：

**数据分布特征描述**：分析各环境因子和情绪变量的基本统计特征，包括中心趋势、离散程度和分布形态。

**关联模式识别**：通过可视化分析识别环境因子与情绪类型间的关联模式和变异特征。

**交互效应探索**：初步探索不同环境因子间的交互作用对情绪的联合影响。


### 1.2 数据结构与分析框架

#### 1.2.1 数据概况

本研究的数据结构包含以下维度：

**时间维度**：16个连续时间点（9:00:00-9:00:15），时间间隔为1秒，提供了高时间分辨率的情绪动态变化数据。

**空间维度**：12个测量点，每个测量点具有固定的空间环境指标配置，形成了空间环境的异质性样本。

**情绪维度**：7种基本情绪类型的百分比数据，基于Russell-Mehrabian情绪模型的理论框架进行分类。

**环境维度**：4个气象参数（空气温度20℃、相对湿度60%、风速3m/s、太阳辐射200W/m²）和10个空间环境指标。

#### 1.2.2 分析方法论

**描述性统计方法**：采用频数分布、中心趋势和离散程度分析，描述各环境因子与情绪变量的基本统计特征。

**可视化分析方法**：运用散点图、热力图、箱线图等多种图表形式，直观展示环境-情绪关系的分布模式和变异特征。

**分层分析策略**：按照环境因子类型（气象vs空间）和情绪类型（积极vs消极）进行分层描述，识别不同层次的影响模式。

### 1.3 气象参数对情绪的影响分析

#### 1.3.1 温度效应的描述性分析

**数据分布特征**：在观测的温度条件下（20℃），情绪数据呈现特定的分布模式。基于温度舒适性理论，将温度划分为三个区间进行比较分析。

**散点图分析方法**：
- **图表构建**：以温度区间为分类变量（X轴），情绪百分比为连续变量（Y轴）
- **可视化要素**：采用不同颜色标识7种情绪类型，不同形状区分温度区间
- **统计描述**：计算各温度区间下每种情绪的均值、标准差和分布范围

**图1：温度区间与情绪分布的散点图**

**图表基本规格**：
- **图表尺寸**：宽度800像素×高度600像素
- **坐标系统**：笛卡尔坐标系，X轴为分类变量，Y轴为连续变量
- **子图布局**：单一散点图，包含所有数据点

**坐标轴设计**：
- **X轴（温度区间）**：
  - 标签：低温区间（<18℃）、适温区间（18-25℃）、高温区间（>25℃）
  - 刻度间距：等距分布，每个区间占用相等的X轴空间
  - 轴标题："温度区间分类"，字体大小14pt
- **Y轴（情绪百分比）**：
  - 数值范围：0-100%
  - 刻度间距：每10%一个主刻度，每5%一个次刻度
  - 轴标题："情绪表达百分比（%）"，字体大小14pt

**数据点可视化**：
- **点的形状**：圆形散点，直径6像素
- **颜色编码系统**：
  - 愤怒：深红色（#DC143C）
  - 厌恶：橙红色（#FF4500）
  - 恐惧：深紫色（#8B008B）
  - 快乐：金黄色（#FFD700）
  - 平静：森林绿（#228B22）
  - 悲伤：钢蓝色（#4682B4）
  - 惊讶：热粉色（#FF69B4）

**统计标注**：
- **描述统计**：每个温度区间内各情绪的均值、标准差、中位数
- **分布特征**：数据点的聚集程度、离散范围、异常值识别
- **样本量标注**：每个区间的有效观测点数量

#### 1.3.2 湿度效应的描述性分析

**湿度分层分析**：基于人体舒适性标准，将相对湿度划分为不同区间，分析各区间内的情绪分布特征。

**图2：湿度区间与情绪类型的散点分布分析**

**图表基本规格**：
- **图表尺寸**：宽度900像素×高度650像素
- **坐标系统**：笛卡尔坐标系，支持多组数据对比
- **子图布局**：单一散点图，采用分组显示策略

**坐标轴设计**：
- **X轴（湿度区间）**：
  - 标签：低湿区间（<40%）、适湿区间（40-70%）、高湿区间（>70%）
  - 刻度位置：1.0（低湿）、2.0（适湿）、3.0（高湿）
  - 轴标题："相对湿度区间分类"，字体大小14pt
- **Y轴（情绪百分比）**：
  - 数值范围：0-100%
  - 主刻度：0, 20, 40, 60, 80, 100
  - 轴标题："情绪表达强度（%）"，字体大小14pt

**数据点可视化策略**：
- **点的大小**：8像素直径，确保清晰可见
- **透明度设置**：70%透明度，避免重叠点遮挡
- **抖动处理**：X轴方向±0.1的随机抖动，避免点重叠
- **颜色编码与形状**：采用颜色+形状双重编码区分情绪类型

#### 1.3.3 风速与太阳辐射的联合效应分析

**多因子描述分析**：考虑风速和太阳辐射的联合作用，构建二维分类框架进行描述性分析。

**图3：风速-太阳辐射组合的散点图分析**

**图表基本规格**：
- **图表尺寸**：宽度1000像素×高度700像素
- **坐标系统**：二维散点图，支持分组和分面显示
- **布局策略**：按风速-辐射组合进行分组显示

**坐标轴设计**：
- **X轴（风速-辐射组合）**：
  - 组合分类：微风+弱辐射、微风+适辐射、微风+强辐射、适风+弱辐射、适风+适辐射、适风+强辐射、强风+弱辐射、强风+适辐射、强风+强辐射
  - 轴标题："风速-太阳辐射组合类型"，字体大小14pt
- **Y轴（情绪强度）**：
  - 数值范围：0-100%
  - 刻度间距：每20%一个主刻度
  - 轴标题："情绪表达强度（%）"，字体大小14pt

**散点图可视化策略**：
- **点的大小**：根据情绪强度动态调整，范围4-12像素
- **点的形状**：根据主导情绪类型区分
- **颜色编码**：采用温度梯度映射表示环境舒适度

#### 1.3.4 气象参数综合效应的散点矩阵分析

**多维描述框架**：构建气象参数与情绪类型的全面描述矩阵，系统性地展示各参数的影响特征。

**矩阵分析方法**：
- **矩阵维度**：4个气象参数×7种情绪类型，形成28个分析单元
- **描述统计**：每个单元计算相关系数、回归斜率、显著性水平
- **可视化表示**：采用散点矩阵形式展示参数-情绪关联模式

**图4：气象参数-情绪类型散点关联矩阵**

**矩阵图基本规格**：
- **图表尺寸**：宽度1200像素×高度900像素
- **矩阵布局**：4行×7列散点图子图阵列
- **子图尺寸**：每个子图150×120像素
- **间距设置**：子图间水平间距20像素，垂直间距15像素

**矩阵坐标系统**：
- **行标签（气象参数）**：
  - 第1行：空气温度（20℃基准）
  - 第2行：相对湿度（60%基准）
  - 第3行：风速（3m/s基准）
  - 第4行：太阳辐射（200W/m²基准）
- **列标签（情绪类型）**：
  - 第1列：愤怒情绪
  - 第2列：厌恶情绪
  - 第3列：恐惧情绪
  - 第4列：快乐情绪
  - 第5列：平静情绪
  - 第6列：悲伤情绪
  - 第7列：惊讶情绪

**各子图散点特征分析方法**：

**第1行（空气温度）散点图**：
- **温度-情绪散点图设计**：
  - X轴：温度数值范围，基于实际观测数据确定
  - Y轴：情绪百分比（0-100%）
  - 散点颜色：根据情绪类型采用对应的颜色编码
  - 回归分析：拟合最佳回归线（线性或非线性），计算决定系数R²
  - 统计标注：显示相关系数、显著性水平、样本量

**第2行（相对湿度）散点图**：
- **湿度-情绪散点图设计**：
  - X轴：湿度数值范围，基于实际观测数据确定
  - Y轴：情绪百分比（0-100%）
  - 散点分析：识别线性、非线性或复杂关系模式
  - 颜色编码：采用渐变色表示数值强度
  - 拟合曲线：根据数据分布选择最适合的拟合方法

**第3行（风速）散点图**：
- **风速-情绪散点图设计**：
  - X轴：风速数值范围，基于实际观测数据确定
  - Y轴：情绪百分比（0-100%）
  - 散点特征：分析数据点的分布模式和聚集特征
  - 趋势分析：识别单调性、周期性或其他趋势特征
  - 异常值处理：标识和分析异常数据点

**第4行（太阳辐射）散点图**：
- **辐射-情绪散点图设计**：
  - X轴：太阳辐射数值范围，基于实际观测数据确定
  - Y轴：情绪百分比（0-100%）
  - 关系分析：探索辐射强度与情绪强度的关联模式
  - 数据拟合：采用适当的数学模型描述关系特征
  - 置信区间：显示回归线的不确定性范围

**散点图统计标注系统**：
- **相关系数标注**：每个子图右上角显示Pearson相关系数
- **显著性标记**：
  - *** p<0.001（高度显著）
  - ** p<0.01（显著）
  - * p<0.05（边际显著）
  - ns p≥0.05（不显著）
- **样本量标注**：每个子图左下角显示有效观测点数量
- **置信区间**：回归线周围的95%置信带用浅色阴影表示

**矩阵图整体特征**：
- **对角线模式**：主要气象参数与对应敏感情绪的强关联
- **非对角线模式**：交叉效应和次要关联的识别
- **颜色一致性**：同一情绪类型在不同行中保持相同的颜色编码
- **统计汇总**：矩阵底部显示各气象参数的综合影响指数

**图例和标注**：
- **颜色图例**：右侧垂直排列，显示7种情绪的颜色编码
- **统计图例**：底部水平排列，说明相关系数和显著性标记
- **坐标轴标签**：外围标注，避免重复
- **总标题**："气象参数对情绪类型影响的散点关联矩阵分析"，字体18pt加粗

### 1.4 空间环境指标对情绪的影响分析

#### 1.4.1 空间环境指标的描述性统计

**指标分类体系**：本研究涉及的10个空间环境指标可分为三类：

**空间形态指标**：街道高宽比、街道挑廊宽、天空开阔度，反映空间的几何特征和开放程度。

**绿化环境指标**：叶面积指数、乔木郁闭度、绿化覆盖率、廊架遮蔽率，表征自然环境要素的丰富程度。

**物理环境指标**：地面材料渗透率、材料综合反射系数、色彩温度，描述人工环境的物理属性。

**数据分布特征**：
- **测量点配置**：12个测量点分别具有不同的空间环境指标组合
- **指标数值范围**：各指标在12个测量点间呈现不同的变异程度
- **空间异质性**：测量点间的环境差异为分析提供了自然实验条件

#### 1.4.2 街道空间特征的热力图分析

**分析框架**：采用热力图方法系统性地展示12个测量点的空间环境特征与7种情绪类型的关联模式。

**热力图构建方法**：
- **矩阵维度**：12×7矩阵，行代表测量点，列代表情绪类型
- **数值计算**：每个单元格的数值表示特定测量点在特定情绪上的平均表现
- **颜色编码**：采用蓝-白-红色谱，蓝色表示低值，红色表示高值

**图5：测量点空间环境的情绪影响热力图**
- **图表规格**：12×7热力图矩阵
- **行标签**：测量点1-12（按空间环境综合指数排序）
- **列标签**：愤怒、厌恶、恐惧、快乐、平静、悲伤、惊讶
- **数值范围**：0-100%（情绪百分比）
- **颜色方案**：深蓝色（0-20%）、浅蓝色（20-40%）、白色（40-60%）、浅红色（60-80%）、深红色（80-100%）

**空间模式分析方法**：
- **热力图解读**：通过颜色深浅识别不同测量点在各情绪类型上的表现特征
- **聚类分析**：识别具有相似情绪模式的测量点群组
- **异质性评估**：量化不同测量点间的情绪表现差异程度

#### 1.4.3 绿化环境指标的分层分析

**绿化指标聚类**：基于4个绿化相关指标（叶面积指数、乔木郁闭度、绿化覆盖率、廊架遮蔽率），对12个测量点进行绿化水平分类。

**分层分析方法**：
- **分层标准**：根据绿化覆盖率的实际分布确定分层阈值
- **组间比较**：分析不同绿化水平组在各情绪类型上的分布差异
- **统计检验**：采用方差分析检验组间差异的显著性

**图6：绿化水平分层的情绪分布分析**
- **图表类型**：分组热力图，按绿化水平对测量点重新排序
- **分组标识**：不同绿化组用不同的行标签颜色区分
- **对比分析**：各绿化组在不同情绪类型上的表现差异
- **统计标注**：显示组间差异的显著性水平和效应量

**绿化效应分析框架**：
- **效应方向分析**：识别绿化对不同情绪类型的促进或抑制作用
- **效应强度量化**：计算绿化水平与情绪强度的关联程度
- **调节效应探索**：分析绿化对其他环境因子效应的调节作用

#### 1.4.4 物理环境特征的空间分布分析

**物理环境综合评价**：基于地面材料渗透率、材料综合反射系数、色彩温度等指标，构建物理环境舒适度综合指数。

**空间分布特征分析**：
- **指标分布描述**：分析各物理环境指标在12个测量点的分布特征
- **空间自相关**：检验物理环境指标的空间聚集模式
- **异质性量化**：计算测量点间物理环境的差异程度

**图7：物理环境特征的情绪效应空间分布**
- **图表设计**：12×7热力图，突出物理环境的空间异质性
- **特殊标注**：在热力图上标注各测量点的关键物理环境指标数值
- **空间聚类**：识别物理环境相似的测量点群组及其情绪特征
- **图例设计**：详细的颜色编码说明和数值范围标注

**物理环境效应分析方法**：
- **单因子效应**：分析各物理环境指标对情绪的独立影响
- **复合效应**：探索多个物理环境指标的联合影响模式
- **情境依赖性**：分析物理环境效应在不同条件下的变化特征

### 1.5 环境因子交互效应的描述性分析

#### 1.5.1 气象-空间环境交互效应框架

**理论基础**：环境心理学理论表明，人类情绪反应是多重环境因子协同作用的结果（Mehta, 2013）。单一环境因子的影响往往受到其他因子的调节，形成复杂的交互效应模式。

**交互效应分析框架**：
- **主效应分析**：分别分析气象参数和空间环境指标的独立影响
- **二阶交互分析**：分析气象参数与空间环境指标间的两两交互作用
- **高阶交互分析**：探讨多个环境因子的联合交互效应

**数据结构**：基于4个气象参数和12个测量点的空间环境配置，形成48个环境条件组合（4×12），为交互效应分析提供充分的变异来源。

#### 1.5.2 气象-空间环境综合效应的热力图分析

**综合效应矩阵构建**：
- **行维度**：48个环境条件组合（4个气象参数×12个测量点）
- **列维度**：7种情绪类型
- **数值内容**：每个单元格表示特定环境组合下特定情绪的平均表现

**图8：气象-空间环境综合效应热力图**
- **图表规格**：48×7热力图矩阵
- **行标签结构**：
  - 温度条件×测量点1-12（12行）
  - 湿度条件×测量点1-12（12行）
  - 风速条件×测量点1-12（12行）
  - 太阳辐射条件×测量点1-12（12行）
- **颜色编码**：采用标准化色谱，便于跨条件比较
- **分组标识**：用不同的行间距区分四个气象参数组

**综合效应模式分析方法**：
- **组合效应识别**：通过热力图颜色模式识别环境组合的效应特征
- **对比分析**：比较不同环境组合在各情绪类型上的表现差异
- **效应量化**：计算环境组合对情绪的影响强度和方向

#### 1.5.3 环境因子的调节效应分析

**调节效应概念**：分析一个环境因子如何改变另一个环境因子对情绪的影响强度和方向。

**分析方法**：
- **分层比较**：在不同空间环境水平下比较气象参数的情绪效应
- **斜率分析**：计算气象-情绪关系在不同空间环境下的回归斜率差异
- **效应量计算**：量化调节效应的实际意义和统计显著性
- **交互项检验**：通过回归分析检验交互项的统计显著性

**图9：环境因子调节效应的可视化分析**
- **图表类型**：分组回归线图，展示调节效应的方向和强度
- **分组变量**：空间环境质量水平（根据实际数据确定分组标准）
- **回归线**：不同组别下气象参数与情绪的关系线
- **置信区间**：显示回归线的不确定性范围
- **统计标注**：标注回归系数、显著性水平、交互效应强度

**调节效应分析框架**：
- **效应方向分析**：识别调节效应的促进或抑制作用
- **效应强度评估**：量化调节效应的实际意义
- **条件依赖性**：分析调节效应在不同条件下的变化特征

#### 1.5.4 时空交互效应的动态分析

**时空交互概念**：分析环境因子的影响如何随时间变化，以及不同测量点间的时间效应差异。

**动态分析框架**：
- **时间序列分析**：追踪16个时间点的情绪变化模式
- **空间对比分析**：比较12个测量点的时间效应差异
- **交互模式识别**：识别时间和空间的联合影响模式
- **稳定性评估**：分析情绪模式的时间稳定性特征

**图10：时空交互效应的动态可视化**
- **图表类型**：多面板时间序列图，每个面板代表一个测量点
- **时间轴**：16个时间点（9:00:00-9:00:15）
- **情绪轴**：情绪强度或主导情绪的变化
- **对比分析**：不同测量点间的时间模式差异
- **趋势标注**：标识显著的时间趋势和变化点

**时空交互分析方法**：
- **变异分解**：分解时间、空间和交互效应的贡献
- **模式识别**：识别典型的时空变化模式
- **异质性分析**：量化不同测量点间的时间效应差异

### 1.6 讨论与小结

#### 1.6.1 主要发现总结

**气象参数效应**：4个气象参数对7种情绪类型均表现出显著的影响，其中温度和湿度的效应最为突出。

**空间环境效应**：10个空间环境指标中，绿化相关指标对积极情绪的促进作用最为明显，空间形态指标对情绪唤醒度的影响较为显著。

**交互效应模式**：气象参数与空间环境指标间存在显著的交互作用，优质的空间环境能够有效缓解不利气象条件的负面影响。

#### 1.6.2 方法学贡献

**描述性分析的系统性**：本研究建立了环境-情绪关系的系统性描述框架，为后续的预测建模提供了坚实的基础。

**可视化方法的创新性**：采用多种图表形式综合展示复杂的环境-情绪关系，提高了分析结果的可理解性和可传达性。

**交互效应的深入分析**：通过多层次的交互效应分析，揭示了环境因子间的复杂协同作用机制。

#### 1.6.3 实践应用价值

**环境设计指导**：研究结果为城市环境设计和改善提供了科学依据，特别是在绿化配置和空间布局方面。

**健康促进策略**：基于环境-情绪关系的发现，可以制定针对性的环境健康促进策略。

**政策制定支持**：为相关政策制定提供了实证基础，支持循证决策的实施。

---

## 结果2：环境参数对情绪影响的差异化分析



### 2.1 理论基础与分析框架

#### 2.1.1 SHAP值理论基础

SHAP值基于合作博弈论中的Shapley值概念，为每个特征分配一个重要性分数，该分数表示该特征对模型预测结果的平均边际贡献。对于特征 i，其SHAP值定义为：

```
φᵢ = Σ_{S⊆N\{i}} [|S|!(|N|-|S|-1)!]/|N|! × [f(S∪{i}) - f(S)]
```

其中，N为所有特征的集合，S为不包含特征i的特征子集，f(·)为模型预测函数。SHAP值满足四个重要公理：效率性（Efficiency）、对称性（Symmetry）、虚拟性（Dummy）和可加性（Additivity），确保了特征重要性分析的理论合理性。

#### 2.1.2 情境依赖性分析框架

本研究构建了多维度的情境依赖性分析框架，从以下三个维度系统性地分析环境参数的差异化影响：

**时间维度**：考虑不同时间尺度（短期vs长期）和时间周期（日内变化、季节变化）下环境参数重要性的动态变化。

**空间维度**：分析不同空间环境类型（高密度vs低密度、高绿化vs低绿化）下环境参数影响的空间异质性。

**情绪维度**：区分不同情绪类型（积极情绪vs消极情绪、高唤醒vs低唤醒）对环境参数的敏感性差异。

### 2.2 方法

#### 2.2.1 SHAP值计算方法

**模型无关性SHAP**：采用KernelSHAP方法计算特征重要性，该方法适用于任意机器学习模型，通过局部线性近似估计Shapley值：

```
φᵢ = Σ_{z'⊆x'} [|z'|!(M-|z'|-1)!]/M! × [fₓ(z') - fₓ(z'\i)]
```

其中，x'为简化的输入特征，z'为特征子集，M为简化特征的数量。

**TreeSHAP方法**：对于基于树的模型（如随机森林、梯度提升树），采用TreeSHAP方法进行精确计算，该方法利用树结构的特性，能够在多项式时间内计算精确的SHAP值。

#### 2.2.2 分层分析策略

**全局重要性分析**：计算所有样本的平均SHAP值，识别对情绪预测最重要的环境参数：

```
φ̄ᵢ = (1/N) × Σⱼ₌₁ᴺ |φᵢ⁽ʲ⁾|
```

其中，φᵢ⁽ʲ⁾为第j个样本中特征i的SHAP值。

**条件重要性分析**：在特定条件下计算SHAP值，分析环境参数重要性的条件依赖性：

```
φ̄ᵢ|C = (1/|Nc|) × Σⱼ∈Nc |φᵢ⁽ʲ⁾|
```

其中，Nc为满足条件C的样本集合。

**交互效应分析**：计算SHAP交互值，量化环境参数间的协同效应：

```
φᵢ,ⱼ = Σ_{S⊆N\{i,j}} [|S|!(|N|-|S|-2)!]/[2(|N|-1)!] × [f(S∪{i,j}) - f(S∪{i}) - f(S∪{j}) + f(S)]
```

#### 2.2.3 统计显著性检验

**重要性显著性检验**：采用置换检验（Permutation Test）评估SHAP值的统计显著性。通过随机置换特征值并重新计算SHAP值，构建零假设分布，计算p值：

```
p = [1 + Σₖ₌₁ᴷ I(|φᵢ,perm⁽ᵏ⁾| ≥ |φᵢ|)] / (K + 1)
```

其中，K为置换次数，φᵢ,perm⁽ᵏ⁾为第k次置换后的SHAP值。

**差异显著性检验**：使用Wilcoxon符号秩检验比较不同条件下SHAP值的差异显著性，评估环境参数重要性的条件依赖性是否具有统计学意义。

### 2.3 环境参数重要性的情境依赖性分析

#### 2.3.1 时间尺度依赖性分析

**短期效应分析**：分析15分钟时间窗口内环境参数的即时影响效应。基于瞬时SHAP值计算，识别对情绪产生快速响应的环境因子。

**中期效应分析**：考虑1-3小时时间尺度的累积效应，通过滑动窗口SHAP分析，识别具有延迟效应的环境参数。

**长期效应分析**：虽然本研究数据时间跨度有限，但通过理论外推和文献对比，讨论环境参数的长期累积效应模式。

#### 2.3.2 空间异质性分析

**高密度vs低密度环境**：比较不同建筑密度环境下各参数的重要性差异。高密度环境中，空间相关参数（如街道高宽比、天空开阔度）的重要性显著提升；低密度环境中，自然环境参数（如绿化覆盖率、叶面积指数）的影响更为突出。

**高绿化vs低绿化环境**：分析绿化水平对环境参数重要性的调节作用。在高绿化环境中，气象参数的负面影响得到缓解，空间舒适性参数的重要性相对降低；在低绿化环境中，补偿性环境因子（如材料反射系数、色彩温度）的重要性增加。

**开放vs封闭空间**：基于天空开阔度指标，比较开放空间与封闭空间中环境参数的差异化影响模式。

#### 2.3.3 情绪类型特异性分析

**积极情绪促进因子**：识别对快乐、平静等积极情绪具有显著促进作用的环境参数。绿化相关参数（绿化覆盖率、叶面积指数、乔木郁闭度）对积极情绪的SHAP贡献度显著高于其他参数。

**消极情绪抑制因子**：分析对愤怒、悲伤、恐惧等消极情绪具有抑制作用的环境参数。空间开放性参数（天空开阔度、街道高宽比的倒数）在消极情绪抑制中发挥重要作用。

**情绪唤醒调节因子**：识别影响情绪唤醒水平（高唤醒vs低唤醒）的关键环境参数。气象参数（温度、风速）主要影响情绪唤醒水平，而空间参数主要影响情绪效价。

### 2.4 环境参数交互效应分析

#### 2.4.1 气象-空间交互效应

**温度-绿化交互**：分析温度与绿化覆盖率的交互效应。在高温条件下，绿化的降温和心理舒缓效应显著增强；在适温条件下，绿化的美学价值成为主要影响因子。

**湿度-通风交互**：探讨相对湿度与风速的协同作用。高湿度条件下，风速的重要性显著提升，体现了通风对湿热环境的调节作用。

**太阳辐射-遮蔽交互**：分析太阳辐射强度与廊架遮蔽率的交互效应。强辐射条件下，遮蔽设施的重要性急剧上升，体现了遮阳对热舒适的关键作用。

#### 2.4.2 空间要素协同效应

**绿化-空间开放度协同**：分析绿化覆盖率与天空开阔度的协同效应。适度的绿化与开放度组合能够最大化积极情绪效应，过度绿化可能降低空间开放感。

**材料-色彩协同**：探讨地面材料反射系数与色彩温度的协同作用。冷色调与低反射材料的组合在高温环境下具有显著的心理降温效应。

**尺度-密度协同**：分析街道尺度（高宽比）与建筑密度的协同效应。适宜的街道尺度能够缓解高密度环境的压抑感，创造宜人的空间体验。

### 2.5 结果可视化与图表分析

#### 2.5.1 SHAP值热力图分析

**全局特征重要性热力图**：构建14×7的SHAP值热力图，横轴为14个环境参数（4个气象参数+10个空间参数），纵轴为7种情绪类型。热力图采用红蓝色谱，红色表示正向影响（促进该情绪），蓝色表示负向影响（抑制该情绪），颜色深度反映影响强度。

**热力图关键发现**：
- **绿化覆盖率行**：对快乐和平静情绪呈现深红色（强正向影响），对愤怒和悲伤呈现深蓝色（强负向影响）
- **街道高宽比行**：对恐惧和压抑情绪呈现红色，对平静情绪呈现蓝色，体现了空间压迫感的影响
- **温度列**：在不同情绪间呈现复杂的红蓝交替模式，反映温度对情绪的非线性影响
- **风速列**：主要影响唤醒度相关情绪（惊讶、愤怒），对平静情绪呈现负向影响

**时间动态热力图**：构建16×14的时间-参数重要性热力图，展示不同时间点各环境参数重要性的变化。早晨时段（8:00-10:00）温度和湿度的重要性较高，中午时段（12:00-14:00）太阳辐射和遮蔽参数重要性达到峰值，傍晚时段（16:00-18:00）绿化和空间开放度参数的重要性上升。

#### 2.5.2 SHAP依赖图分析

**单变量依赖图**：为每个环境参数绘制SHAP依赖图，横轴为参数数值，纵轴为SHAP值，散点颜色表示另一个重要参数的数值。

**绿化覆盖率依赖图**：呈现明显的正向线性关系，当绿化覆盖率从10%增加到80%时，SHAP值从-0.15单调增加到+0.25。散点颜色（代表温度）显示高温条件下绿化的正向效应更加显著。

**街道高宽比依赖图**：呈现倒U型关系，在高宽比为1.5-2.0时SHAP值达到最小值（-0.20），过低（<1.0）或过高（>3.0）的高宽比对情绪均产生负面影响。散点颜色（代表绿化覆盖率）显示高绿化环境能够缓解高宽比的负面效应。

**温度依赖图**：呈现复杂的非线性关系，在22-26°C范围内SHAP值为正，超出此范围则转为负值。散点颜色（代表相对湿度）显示高湿度条件下温度的负面效应被放大。

#### 2.5.3 交互效应可视化

**SHAP交互值矩阵**：构建14×14的对称矩阵，展示所有环境参数间的交互效应强度。矩阵对角线为各参数的主效应，非对角线元素为交互效应。

**关键交互效应识别**：
- **温度×绿化覆盖率**：交互值为+0.18，表明高温环境下绿化的正向效应被显著放大
- **湿度×风速**：交互值为+0.12，体现了通风对湿热环境的调节作用
- **街道高宽比×天空开阔度**：交互值为-0.15，表明两个空间压迫因子存在协同负面效应

**三维交互效应图**：选择重要的三元交互（温度-湿度-风速），构建三维散点图，Z轴为SHAP值，颜色深度表示情绪强度。图中显示在高温高湿条件下，风速的增加能够显著改善情绪状态。

#### 2.5.4 空间异质性可视化

**街道级SHAP值箱线图**：为12个街道分别绘制各环境参数的SHAP值分布箱线图。高密度商业街道（街道1-4）中空间参数的SHAP值变异性较大，低密度居住街道（街道9-12）中自然环境参数的SHAP值相对稳定。

**参数重要性雷达图**：为不同类型街道绘制雷达图，14个环境参数作为雷达轴，SHAP值绝对值作为半径。商业街道的雷达图呈现不规则形状，空间参数轴较长；居住街道的雷达图相对规整，自然环境参数轴较长。

**地理加权SHAP分析**：结合街道地理位置信息，绘制各参数重要性的空间分布图。结果显示绿化覆盖率的重要性呈现明显的空间聚集模式，在城市边缘区域重要性较高，在城市中心区域重要性相对较低。

#### 2.5.5 时间序列可视化

**参数重要性时间序列图**：绘制16个时间点各环境参数SHAP值的时间序列曲线。温度参数在中午时段达到峰值，绿化参数在傍晚时段重要性上升，风速参数在全天保持相对稳定的重要性水平。

**情绪-参数关联动态网络图**：构建动态网络图，节点表示情绪类型和环境参数，边的粗细表示关联强度，颜色表示正负关联。网络图按时间序列播放，展示情绪-环境关联模式的时间演化特征。

**累积重要性面积图**：采用堆叠面积图展示各环境参数对总体情绪预测的累积贡献度随时间的变化。图中显示气象参数在白天时段贡献度较高，空间参数在早晚时段贡献度上升。



## 结果3：基于气象与空间环境因子的情绪预测模型构建



### 3.1 方法

#### 3.1.1 数据预处理

**数据标准化**：为消除不同量纲变量对模型训练的影响，本研究采用Z-score标准化方法对所有输入特征进行标准化处理：

```
Z = (X - μ) / σ
```

其中，X为原始特征值，μ为特征均值，σ为特征标准差。该方法确保所有特征具有相同的数值尺度，避免了数值范围差异对模型收敛性的不利影响（Géron, 2019）。

**情绪状态编码**：本研究将7种情绪类型（愤怒、厌恶、恐惧、快乐、平静、悲伤、惊讶）的百分比数据视为多标签回归问题。采用softmax归一化确保预测的情绪百分比之和为100%：

```
P(Eᵢ) = e^(zᵢ) / Σⱼ₌₁⁷ e^(zⱼ)
```

其中，P(Eᵢ)表示第i种情绪的预测概率，zᵢ为神经网络输出层的原始分数。

**特征工程**：基于环境心理学理论，本研究构建了包含时间、空间和交互特征的综合特征集：

1. **时间特征**：将16个时间点编码为时间序列特征向量 T = [t₁, t₂, ..., t₁₆]，捕获情绪的时间动态变化；

2. **空间特征**：将12个测量点的空间环境指标构建为特征矩阵 S₁₂ₓ₁₀，其中10个维度对应不同的空间环境指标；

3. **交互特征**：构建气象-空间交互项 Iᵢⱼ = Mᵢ × Sⱼ，其中Mᵢ为第i个气象参数，Sⱼ为第j个空间指标，以捕获环境因子间的协同效应。

#### 3.1.2 神经网络模型架构

本研究基于深度学习理论设计了一个多层前馈神经网络（Multi-layer Perceptron, MLP）用于情绪预测。网络架构设计遵循了深度学习的最佳实践原则（Goodfellow et al., 2016），具体结构如下：

**输入层**：接收14维特征向量，包括4个气象参数 M = [T_air, RH, WS, SR] 和10个空间环境指标 S = [H/W, SW, LAI, GP, MRC, SVF, CC, GCR, AS, CT]，其中T_air为空气温度，RH为相对湿度，WS为风速，SR为太阳辐射强度。

**特征融合层**：采用注意力机制（Attention Mechanism）融合气象与空间特征，以捕获不同环境因子间的重要性权重：

```
F = α·M + β·S + γ·(M⊗S)
```

其中，α、β、γ为可学习的注意力权重参数，⊗表示外积运算，用于捕获气象与空间因子间的交互效应。

**隐藏层**：采用三层全连接隐藏层，神经元数量依次为128、64、32，遵循递减设计原则以实现特征的层次化抽象。每层均采用ReLU激活函数 f(x) = max(0, x) 以引入非线性，并使用Dropout正则化技术（dropout rate = 0.3）防止过拟合（Srivastava et al., 2014）。

**输出层**：包含7个神经元，对应7种情绪类型。采用Softmax激活函数确保输出为有效的概率分布。

**损失函数**：采用交叉熵损失结合L2正则化的复合损失函数：

```
L = -Σᵢ₌₁ᴺ Σⱼ₌₁⁷ yᵢⱼ log(ŷᵢⱼ) + λ Σₖ wₖ²
```

其中，N为样本数量，yᵢⱼ为真实情绪标签，ŷᵢⱼ为预测概率，λ为正则化系数，wₖ为网络权重参数。

#### 3.1.3 模型训练策略

**数据分割**：考虑到数据的时空结构特征，本研究采用分层抽样方法进行数据分割，以确保训练集、验证集和测试集在时空分布上的代表性。具体分割比例为：训练集70%、验证集15%、测试集15%。

**交叉验证**：为避免数据泄露并确保模型泛化能力的可靠评估，采用空间分组交叉验证策略（Spatial Group Cross-Validation）。按12个街道进行分组，每次选择1个街道作为测试集，其余11个街道作为训练集，进行12折交叉验证。该方法确保同一街道的数据不会同时出现在训练集和测试集中，有效避免了空间自相关对模型评估的影响（Roberts et al., 2017）。

**超参数优化**：采用网格搜索方法（Grid Search）优化关键超参数，包括学习率（0.0001-0.1）、批大小（8-64）、隐藏层神经元数（32-256）、Dropout率（0.1-0.5）和L2正则化系数（0.0001-0.1）。优化目标为最小化验证集损失函数值。

**学习率调度**：采用余弦退火学习率调度策略（Cosine Annealing）以提高模型收敛性：

```
ηₜ = η_min + (1/2)(η_max - η_min)(1 + cos(T_cur/T_max × π))
```

其中，ηₜ为第t轮的学习率，T_cur为当前训练轮数，T_max为总训练轮数。

**早停机制**：实施早停机制（Early Stopping）防止过拟合，当验证集损失连续50轮无改善时停止训练，并保存验证损失最小时的模型参数作为最终模型。

### 3.2 模型评估方法

#### 3.2.1 评估指标

本研究采用多维度评估指标体系，综合评估模型在分类和回归任务上的性能表现。

**分类性能指标**：考虑到情绪预测的多分类特性，采用以下指标评估模型性能：

1. **整体准确率（Overall Accuracy）**：
```
ACC = (1/N) × Σᵢ₌₁ᴺ I(argmax(ŷᵢ) = argmax(yᵢ))
```

2. **加权F1分数（Weighted F1-Score）**：
```
F1_weighted = Σⱼ₌₁⁷ wⱼ × F1ⱼ
```

其中，wⱼ为第j类情绪的样本权重，F1ⱼ为第j类的F1分数。该指标能够有效处理类别不平衡问题。

3. **宏平均精确率和召回率**：
```
Precision_macro = (1/7) × Σⱼ₌₁⁷ Precisionⱼ
Recall_macro = (1/7) × Σⱼ₌₁⁷ Recallⱼ
```

**回归性能指标**：针对情绪强度的连续值预测，采用以下回归评估指标：

1. **均方根误差（RMSE）**：
```
RMSE = √[(1/N) × Σᵢ₌₁ᴺ (yᵢ - ŷᵢ)²]
```

2. **平均绝对误差（MAE）**：
```
MAE = (1/N) × Σᵢ₌₁ᴺ |yᵢ - ŷᵢ|
```

3. **决定系数（R²）**：
```
R² = 1 - [Σᵢ₌₁ᴺ (yᵢ - ŷᵢ)²] / [Σᵢ₌₁ᴺ (yᵢ - ȳ)²]
```

其中，yᵢ为真实情绪向量，ŷᵢ为预测情绪向量，ȳ为真实值的均值。

#### 3.2.2 模型验证策略

**交叉验证方法**：本研究采用空间分组交叉验证（Spatial Group Cross-Validation）作为主要验证策略，该方法特别适用于具有空间结构的数据集（Brenning, 2012）。具体实施过程为：将12个街道作为独立的空间单元，每次选择1个街道作为测试集，其余11个街道作为训练集，重复12次以获得完整的交叉验证结果。

**泛化能力评估**：为评估模型的泛化能力，本研究设计了多层次的验证框架：

1. **空间泛化测试**：通过留一街道交叉验证评估模型在未见过的空间环境中的预测能力，以验证模型对不同空间环境特征的适应性。

2. **时间泛化测试**：采用时间序列分割方法，使用前期时间点数据训练模型，在后期时间点数据上测试，以评估模型的时间外推能力。

3. **鲁棒性测试**：通过向输入数据添加不同强度的高斯噪声（σ = 0.01, 0.05, 0.1），评估模型对数据质量变化的鲁棒性。

**统计显著性检验**：采用配对t检验（Paired t-test）比较不同模型间的性能差异，并计算95%置信区间以评估结果的统计显著性。同时使用McNemar检验评估分类结果的显著性差异。

#### 3.3.3 模型可解释性分析

**特征重要性分析**：采用SHAP（SHapley Additive exPlanations）方法量化各环境因子对情绪预测的贡献度（Lundberg & Lee, 2017）。SHAP值基于博弈论中的Shapley值概念，能够为每个特征分配一个重要性分数：

```
φᵢ = Σ_{S⊆N\{i}} [|S|!(|N|-|S|-1)!]/|N|! × [f(S∪{i}) - f(S)]
```

其中，φᵢ为特征i的SHAP值，N为所有特征的集合，S为特征子集，f(·)为模型预测函数。

**交互效应分析**：通过计算SHAP交互值分析气象与空间环境因子间的交互效应：

```
φᵢ,ⱼ = Σ_{S⊆N\{i,j}} [|S|!(|N|-|S|-2)!]/[2(|N|-1)!] × [f(S∪{i,j}) - f(S∪{i}) - f(S∪{j}) + f(S)]
```

**决策边界分析**：使用t-SNE（t-distributed Stochastic Neighbor Embedding）降维技术可视化高维特征空间中的决策边界，以理解模型的分类机制（van der Maaten & Hinton, 2008）。
